package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.componet.checkserver.CheckingWordContext;
import com.estone.erp.publish.amazon.componet.checkserver.template.TemplateCheckingWordService;
import com.estone.erp.publish.amazon.model.AmazonOperateLog;
import com.estone.erp.publish.amazon.service.AmazonOperateLogService;
import com.estone.erp.publish.common.enums.WenAnTypeEnum;
import com.estone.erp.publish.system.erpCommon.module.TrieResultVo;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.CheckOfficialRequest;
import com.estone.erp.publish.system.product.response.OfficialAmazonResponse;
import com.estone.erp.publish.tidb.publishtidb.domain.CheckCopywritingInfringementWordDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewBasisInfoVO;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewSaveDTO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonNewProductCopywritingReviewMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonNewProductCopywritingReview;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonNewProductCopywritingReviewService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Amazon新品文案审核服务实现类
 * 提供文案审核、侵权词检查、相似度验证等功能
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class AmazonNewProductCopywritingReviewServiceImpl extends ServiceImpl<AmazonNewProductCopywritingReviewMapper, AmazonNewProductCopywritingReview> implements AmazonNewProductCopywritingReviewService {

    @Resource
    private ProductClient productClient;

    @Resource
    private TemplateCheckingWordService templateCheckingWordService;

    @Resource
    private AmazonOperateLogService amazonOperateLogService;

    /**
     * 获取审核基础信息
     * 包括Amazon文案数据和ai生成的标题信息
     *
     * @param spu 商品SPU编码
     * @return 审核基础信息VO对象
     */
    @Override
    public ReviewBasisInfoVO getReviewBasisInfo(String spu) {
        validateSpu(spu);
        // 获取Amazon文案数据
        OfficialAmazonResponse amazonData = getAmazonOfficialData(spu);
        ReviewBasisInfoVO vo = ReviewBasisInfoVO.transfer(amazonData);

        // 查询记录
        AmazonNewProductCopywritingReview review = getExistingReview(spu);
        if (review != null) {
            // 设置AI生成的标题
            setAiGeneratedTitles(vo, review);

            // 检查侵权词并设置检查结果
            setInfringementCheckResults(vo, review);
        }

        return vo;
    }


    /**
     * 获取Amazon文案数据
     *
     * @param spu 商品SPU编码
     * @return Amazon文案响应数据
     */
    private OfficialAmazonResponse getAmazonOfficialData(String spu) {
        ApiResult<OfficialAmazonResponse> response = productClient.getCheckAmazonOfficial(spu);

        if (!response.isSuccess()) {
            throw new RuntimeException(response.getErrorMsg());
        }

        OfficialAmazonResponse result = response.getResult();
        if (result == null) {
            throw new RuntimeException(String.format("spu[%s]调用产品系统获取不到Amazon文案", spu));
        }

        return result;
    }

    /**
     * 获取审核记录
     *
     * @param spu 商品SPU编码
     * @return 审核记录，如果不存在则返回null
     */
    private AmazonNewProductCopywritingReview getExistingReview(String spu) {
        LambdaQueryWrapper<AmazonNewProductCopywritingReview> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonNewProductCopywritingReview::getSpu, spu);
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 设置AI生成的标题到VO对象
     *
     * @param vo     审核基础信息VO
     * @param review 审核记录
     */
    private void setAiGeneratedTitles(ReviewBasisInfoVO vo, AmazonNewProductCopywritingReview review) {
        vo.setLongTitle6AiGenerated(review.getLongTitle6AiGenerated());
        vo.setLongTitle7AiGenerated(review.getLongTitle7AiGenerated());
        vo.setLongTitle8AiGenerated(review.getLongTitle8AiGenerated());
    }

    /**
     * 设置侵权词检查结果到VO对象
     *
     * @param vo     审核基础信息VO
     * @param review 审核记录
     */
    private void setInfringementCheckResults(ReviewBasisInfoVO vo, AmazonNewProductCopywritingReview review) {
        ReviewSaveDTO dto = ReviewSaveDTO.builder()
                .spu(review.getSpu())
                .longTitle6Approved(review.getLongTitle6Approved())
                .longTitle7Approved(review.getLongTitle7Approved())
                .longTitle8Approved(review.getLongTitle8Approved())
                .build();

        CheckCopywritingInfringementWordDTO infringementResult = checkCopywritingInfringementWords(dto);
        if (infringementResult != null) {
            vo.setLongTitle6Check(infringementResult.getLongTitle6Check());
            vo.setLongTitle7Check(infringementResult.getLongTitle7Check());
            vo.setLongTitle8Check(infringementResult.getLongTitle8Check());
        }
    }

    /**
     * 保存文案审核结果
     * 包括标题相似度验证、重复性检查、数据保存和操作日志记录
     *
     * @param dto 审核保存数据传输对象
     */
    @Override
    public void saveCopywritingReview(ReviewSaveDTO dto) {
        String currentUser = WebUtils.getUserName();
        String spu = dto.getSpu();

        validateSpu(spu);

        // 获取Amazon数据
        OfficialAmazonResponse amazonData = getAmazonOfficialData(spu);
        ReviewBasisInfoVO vo = ReviewBasisInfoVO.transfer(amazonData);

        // 产品系统文案校验
        validateOfficial(dto);

        // 验证标题相似度
        validateTitleSimilarity(vo, dto);

        // 验证标题重复性
        validateTitleDuplication(dto);

        // 保存或更新审核记录
        AmazonNewProductCopywritingReview review = saveOrUpdateReview(dto, currentUser);

        // 创建操作日志
        createOperateLog(spu, vo, review, currentUser);
    }


    private void validateSpu(String spu) {
        if (StringUtils.isBlank(spu)) {
            throw new RuntimeException("spu不能为空");
        }
    }

    /**
     * 验证标题相似度是否超过70%
     *
     * @param vo  审核基础信息VO
     * @param dto 审核保存数据传输对象
     */
    private void validateTitleSimilarity(ReviewBasisInfoVO vo, ReviewSaveDTO dto) {
        String title6 = dto.getLongTitle6Approved();
        String title7 = dto.getLongTitle7Approved();
        String title8 = dto.getLongTitle8Approved();

        // 构建比较标题列表（原有标题1-5 + 其他两个审核标题）
        List<String> baseTitles = Arrays.asList(
                vo.getLongTitle1(), vo.getLongTitle2(), vo.getLongTitle3(),
                vo.getLongTitle4(), vo.getLongTitle5()
        );

        // 检查标题6的相似度
        if (isTitleSimilarityOver70Percent(title6, baseTitles, title7, title8)) {
            throw new RuntimeException("不允许存在两个标题相似度大于70%");
        }

        // 检查标题7的相似度
        if (isTitleSimilarityOver70Percent(title7, baseTitles, title6, title8)) {
            throw new RuntimeException("不允许存在两个标题相似度大于70%");
        }

        // 检查标题8的相似度
        if (isTitleSimilarityOver70Percent(title8, baseTitles, title6, title7)) {
            throw new RuntimeException("不允许存在两个标题相似度大于70%");
        }
    }

    /**
     * 检查单个标题与其他标题的相似度是否超过70%
     *
     * @param targetTitle 目标标题
     * @param baseTitles  基础标题列表
     * @param otherTitle1 其他标题1
     * @param otherTitle2 其他标题2
     * @return 是否超过70%相似度
     */
    private boolean isTitleSimilarityOver70Percent(String targetTitle, List<String> baseTitles,
                                                   String otherTitle1, String otherTitle2) {
        List<String> compareList = new ArrayList<>(baseTitles);
        compareList.add(otherTitle1);
        compareList.add(otherTitle2);

        return compareList.stream()
                .anyMatch(compareTitle -> ProductUtils.checkSimilar(compareTitle, targetTitle) > 70);
    }

    /**
     * 验证标题重复性
     *
     * @param dto 审核保存数据传输对象
     */
    private void validateTitleDuplication(ReviewSaveDTO dto) {
        List<String> titleList = Arrays.asList(
                dto.getLongTitle6Approved(),
                dto.getLongTitle7Approved(),
                dto.getLongTitle8Approved()
        );
        ProductUtils.validateTitleDuplication(titleList);
    }

    /**
     * 产品系统文案校验
     *
     * @param dto 审核保存数据传输对象
     */
    private void validateOfficial(ReviewSaveDTO dto) {
        CheckOfficialRequest request = CheckOfficialRequest.builder()
                .spu(dto.getSpu())
                .longTitle6(dto.getLongTitle6Approved())
                .longTitle7(dto.getLongTitle7Approved())
                .longTitle8(dto.getLongTitle8Approved())
                .mustKeyword(dto.getMustKeywordApproved())
                .build();
        ApiResult<Void> response = productClient.checkOfficial(request);
        if (!response.isSuccess()) {
            throw new RuntimeException(response.getErrorMsg());
        }
    }


    /**
     * 保存或更新审核记录
     *
     * @param dto         审核保存数据传输对象
     * @param currentUser 当前用户
     * @return 保存后的审核记录
     */
    private AmazonNewProductCopywritingReview saveOrUpdateReview(ReviewSaveDTO dto, String currentUser) {
        LambdaQueryWrapper<AmazonNewProductCopywritingReview> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonNewProductCopywritingReview::getSpu, dto.getSpu());

        AmazonNewProductCopywritingReview review = Optional.ofNullable(baseMapper.selectOne(wrapper))
                .orElse(new AmazonNewProductCopywritingReview());

        // 设置审核数据
        review.setSpu(dto.getSpu());
        review.setLongTitle6Approved(dto.getLongTitle6Approved());
        review.setLongTitle7Approved(dto.getLongTitle7Approved());
        review.setLongTitle8Approved(dto.getLongTitle8Approved());
        review.setMustKeywordApproved(dto.getMustKeywordApproved());
        review.setApprovedBy(currentUser);
        review.setCreatedTime(Optional.ofNullable(review.getCreatedTime()).orElse(LocalDateTime.now()));
        review.setUpdatedTime(LocalDateTime.now());

        // 保存或更新记录
        if (review.getId() == null) {
            baseMapper.insert(review);
        } else {
            baseMapper.updateById(review);
        }

        return review;
    }

    /**
     * 创建操作日志
     *
     * @param spu         商品SPU编码
     * @param vo          审核基础信息VO
     * @param review      审核记录
     * @param currentUser 当前用户
     */
    private void createOperateLog(String spu, ReviewBasisInfoVO vo,
                                  AmazonNewProductCopywritingReview review, String currentUser) {
        //TODO Mark:待确定是否需要对比必选关键词才保存
        AmazonOperateLog operateLog = new AmazonOperateLog();
        operateLog.setMessage(spu);
        operateLog.setFieldName("Amazon新品文案审核-mustKeyword");
        operateLog.setBefore(vo.getMustKeyword());
        operateLog.setAfter(review.getMustKeywordApproved());
        operateLog.setType("AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW");
        operateLog.setCreateBy(currentUser);
        operateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        amazonOperateLogService.insert(operateLog);
    }

    /**
     * 检查文案侵权词
     * 对标题6、7、8进行侵权词和品牌词检查
     *
     * @param dto 审核保存数据传输对象
     * @return 侵权词检查结果DTO
     */
    @Override
    public CheckCopywritingInfringementWordDTO checkCopywritingInfringementWords(ReviewSaveDTO dto) {
        // 验证SPU参数
        validateSpu(dto.getSpu());

        // 检查各个标题的侵权词
        String title6Check = checkTitleInfringement(dto.getLongTitle6Approved(), dto.getSpu());
        String title7Check = checkTitleInfringement(dto.getLongTitle7Approved(), dto.getSpu());
        String title8Check = checkTitleInfringement(dto.getLongTitle8Approved(), dto.getSpu());

        return CheckCopywritingInfringementWordDTO.builder()
                .longTitle6Check(title6Check)
                .longTitle7Check(title7Check)
                .longTitle8Check(title8Check)
                .build();
    }

    /**
     * 检查单个标题的侵权词
     *
     * @param title 待检查的标题
     * @param spu   商品SPU编码
     * @return 侵权词检查结果，多个词用逗号分隔，无侵权词返回null
     */
    private String checkTitleInfringement(String title, String spu) {
        if (StringUtils.isBlank(title)) {
            return null;
        }

        // 创建检查上下文
        CheckingWordContext context = new CheckingWordContext(
                title, "US", spu, WenAnTypeEnum.amazonWenAn.getCode()
        );

        // 调用侵权词检查服务
        ApiResult<TrieResultVo> checkResult = templateCheckingWordService.checkingWord(context);
        if (!checkResult.isSuccess()) {
            throw new RuntimeException(checkResult.getErrorMsg());
        }

        // 收集侵权词和品牌词
        List<String> infringementWords = collectInfringementWords(checkResult.getResult());

        // 返回去重后的侵权词字符串
        String result = infringementWords.stream()
                .distinct()
                .collect(Collectors.joining(","));

        return StringUtils.isBlank(result) ? null : result;
    }

    /**
     * 收集侵权词和品牌词
     *
     * @param trieResult 字典树检查结果
     * @return 侵权词列表
     */
    private List<String> collectInfringementWords(TrieResultVo trieResult) {
        List<String> infringementWords = new ArrayList<>();

        // 添加侵权词
        if (MapUtils.isNotEmpty(trieResult.getInfringementWordSourceMap())) {
            infringementWords.addAll(trieResult.getInfringementWordSourceMap().keySet());
        }

        // 添加品牌词
        if (MapUtils.isNotEmpty(trieResult.getBrandWordSourceMap())) {
            infringementWords.addAll(trieResult.getBrandWordSourceMap().keySet());
        }

        return infringementWords;
    }
}
