package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample;
import com.estone.erp.publish.amazon.service.AmazonMustPublishNewProductService;
import com.estone.erp.publish.system.ai.AiServiceClient;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.response.OfficialAmazonResponse;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewBasisInfoVO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonNewProductCopywritingReview;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonNewProductCopywritingReviewService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Amazon新产品AI文案标题生成定时任务
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-11
 */
@Slf4j
@Component
public class AmazonNewProductCopywritingAiGenerateJob extends AbstractJobHandler {

    @Autowired
    private AmazonMustPublishNewProductService amazonMustPublishNewProductService;

    @Autowired
    private AmazonNewProductCopywritingReviewService amazonNewProductCopywritingReviewService;

    @Autowired
    private AiServiceClient aiServiceClient;

    @Autowired
    private ProductClient productClient;

    public AmazonNewProductCopywritingAiGenerateJob() {
        super("AmazonNewProductCopywritingAiGenerateJob");
    }

    /**
     * 内部参数类
     */
    @Data
    public static class InnerParam {
        /**
         * 指定产品ID
         */
        private Integer id;
        
        /**
         * 指定扫描日期 (格式: yyyy-MM-dd)
         */
        private String date;
    }

    @Override
    @XxlJob("AmazonNewProductCopywritingAiGenerateJob")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("Amazon新产品AI文案标题生成任务开始执行，参数：{}", param);
        
        try {
            // 解析参数
            InnerParam innerParam = parseParam(param);
            
            // 获取需要处理的产品列表
            List<AmazonMustPublishNewProduct> productList = getProductsToProcess(innerParam);
            
            if (CollectionUtils.isEmpty(productList)) {
                XxlJobLogger.log("未找到需要处理的产品数据");
                return ReturnT.SUCCESS;
            }
            
            XxlJobLogger.log("找到 {} 个产品需要生成AI标题", productList.size());
            
            // 处理每个产品
            int successCount = 0;
            int failCount = 0;
            
            for (AmazonMustPublishNewProduct product : productList) {
                try {
                    processProduct(product);
                    successCount++;
                    XxlJobLogger.log("成功处理产品 SPU: {}", product.getSpu());
                } catch (Exception e) {
                    failCount++;
                    log.error("处理产品失败，SPU: {}, 错误: {}", product.getSpu(), e.getMessage(), e);
                    XxlJobLogger.log("处理产品失败，SPU: {}, 错误: {}", product.getSpu(), e.getMessage());
                }
            }
            
            XxlJobLogger.log("任务执行完成，成功: {} 个，失败: {} 个", successCount, failCount);
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("Amazon新产品AI文案标题生成任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 解析任务参数
     */
    private InnerParam parseParam(String param) {
        InnerParam innerParam = new InnerParam();
        
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数解析失败，使用默认参数: {}", e.getMessage());
            }
        }
        
        // 如果没有指定日期，默认使用当天
        if (StringUtils.isBlank(innerParam.getDate())) {
            innerParam.setDate(LocalDate.now().toString());
        }
        
        return innerParam;
    }

    /**
     * 获取需要处理的产品列表
     */
    private List<AmazonMustPublishNewProduct> getProductsToProcess(InnerParam innerParam) {
        AmazonMustPublishNewProductExample example = new AmazonMustPublishNewProductExample();
        AmazonMustPublishNewProductExample.Criteria criteria = example.createCriteria();
        
        // 如果指定了ID，只处理该产品
        if (innerParam.getId() != null) {
            criteria.andIdEqualTo(innerParam.getId());
        } else {
            // 根据日期筛选
            String targetDate = innerParam.getDate();
            try {
                LocalDate date = LocalDate.parse(targetDate);
                String startTime = date.atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String endTime = date.plusDays(1).atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                
                criteria.andCreatedTimeBetween(
                    java.sql.Timestamp.valueOf(startTime),
                    java.sql.Timestamp.valueOf(endTime)
                );
            } catch (Exception e) {
                XxlJobLogger.log("日期解析失败，使用当天数据: {}", e.getMessage());
                String today = LocalDate.now().toString();
                String startTime = LocalDate.parse(today).atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String endTime = LocalDate.parse(today).plusDays(1).atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                
                criteria.andCreatedTimeBetween(
                    java.sql.Timestamp.valueOf(startTime),
                    java.sql.Timestamp.valueOf(endTime)
                );
            }
        }
        
        return amazonMustPublishNewProductService.selectByExample(example);
    }

    /**
     * 处理单个产品
     */
    private void processProduct(AmazonMustPublishNewProduct product) throws Exception {
        String spu = product.getSpu();
        
        // 检查是否已经生成过AI标题
        if (isAlreadyProcessed(spu)) {
            XxlJobLogger.log("产品 {} 已经生成过AI标题，跳过处理", spu);
            return;
        }
        
        // 获取Amazon官方数据
        OfficialAmazonResponse amazonData = getAmazonOfficialData(spu);
        ReviewBasisInfoVO vo = ReviewBasisInfoVO.transfer(amazonData);
        
        // 提取参考标题
        List<String> referenceTitles = extractReferenceTitles(vo);
        
        if (CollectionUtils.isEmpty(referenceTitles)) {
            throw new RuntimeException(String.format("产品 %s 没有可用的参考标题", spu));
        }
        
        // 构建AI请求
        ChatOpenaiRequest aiRequest = buildAiRequest(referenceTitles);
        
        // 调用AI服务
        ApiResult<ChatCompletionResponse> aiResponse = aiServiceClient.sendProcessOrdinaryTencent(aiRequest);
        
        if (!aiResponse.isSuccess()) {
            throw new RuntimeException(String.format("AI服务调用失败: %s", aiResponse.getErrorMsg()));
        }
        
        // 解析AI响应
        List<String> generatedTitles = parseAiResponse(aiResponse.getResult());
        
        // 保存生成的标题
        saveGeneratedTitles(spu, generatedTitles);
    }

    /**
     * 检查产品是否已经处理过
     */
    private boolean isAlreadyProcessed(String spu) {
        LambdaQueryWrapper<AmazonNewProductCopywritingReview> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonNewProductCopywritingReview::getSpu, spu);
        wrapper.and(w -> w.isNotNull(AmazonNewProductCopywritingReview::getLongTitle6AiGenerated)
                .or().isNotNull(AmazonNewProductCopywritingReview::getLongTitle7AiGenerated)
                .or().isNotNull(AmazonNewProductCopywritingReview::getLongTitle8AiGenerated));
        
        return amazonNewProductCopywritingReviewService.count(wrapper) > 0;
    }

    /**
     * 获取Amazon官方数据
     */
    private OfficialAmazonResponse getAmazonOfficialData(String spu) throws Exception {
        ApiResult<OfficialAmazonResponse> response = productClient.getCheckAmazonOfficial(spu);
        
        if (!response.isSuccess()) {
            throw new RuntimeException(String.format("获取Amazon官方数据失败: %s", response.getErrorMsg()));
        }
        
        OfficialAmazonResponse result = response.getResult();
        if (result == null) {
            throw new RuntimeException(String.format("产品 %s 的Amazon官方数据为空", spu));
        }
        
        return result;
    }

    /**
     * 提取参考标题（长标题1-5）
     */
    private List<String> extractReferenceTitles(ReviewBasisInfoVO vo) {
        List<String> titles = new ArrayList<>();
        
        if (StringUtils.isNotBlank(vo.getLongTitle1())) {
            titles.add(vo.getLongTitle1());
        }
        if (StringUtils.isNotBlank(vo.getLongTitle2())) {
            titles.add(vo.getLongTitle2());
        }
        if (StringUtils.isNotBlank(vo.getLongTitle3())) {
            titles.add(vo.getLongTitle3());
        }
        if (StringUtils.isNotBlank(vo.getLongTitle4())) {
            titles.add(vo.getLongTitle4());
        }
        if (StringUtils.isNotBlank(vo.getLongTitle5())) {
            titles.add(vo.getLongTitle5());
        }
        
        return titles;
    }
